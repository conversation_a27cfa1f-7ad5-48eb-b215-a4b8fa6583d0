/*
 * @Description: 图表数据更新相关方法
 */
import * as echarts from "echarts";

/**
 * 更新日生产计划执行率图表数据
 * @param {echarts.ECharts} chart - 图表实例
 * @param {Object} data - 接口返回的数据
 * @param {boolean} darkTheme - 是否为暗色主题
 * @returns {void}
 */
export const updateDeliveryChartData = (chart, data, darkTheme) => {
  if (!chart) return;

  // 文本颜色根据主题设置
  const textColor = darkTheme ? "#ffffff" : "#333333";

  // 清除之前存储的数据
  if (!chart._rawData) {
    chart._rawData = {};
  }

  // 检查数据是否有效
  if (!data || !data.details || !Array.isArray(data.details) || data.details.length === 0) {
    // 设置图表选项 - 没有数据时显示"暂无详细数据"
    chart.setOption({
      xAxis: {
        data: ['暂无详细数据']
      },
      series: [
        {
          name: "目标产量",
          data: [0]
        },
        {
          name: "实际产量",
          data: [0]
        },

        {
          name: "达成率",
          data: [0]
        }
      ]
    });

    // 清除存储的原始数据
    chart._rawData = {};
    return;
  }

  // 保存原始数据，用于点击事件
  chart._rawData = data;

  try {
    // 按照小时排序 - 修复排序逻辑，确保正确处理带前导零的小时
    const sortedDetails = [...data.details]
    // .sort((a, b) => {
    //   // 使用数字比较，但保留原始字符串格式
    //   return Number(a.productHour) - Number(b.productHour);
    // });

    // 提取小时作为X轴数据，保持原始格式
    const xAxisData = sortedDetails.map(item => item.productHour);

    // 提取实际产量和计划产量
    const actualData = sortedDetails.map(item => item.productYeild);
    const planData = sortedDetails.map(item => item.planQty);

    // 计算每小时的达成率
    const rateData = sortedDetails.map(item => {
      // 修正达成率计算逻辑：当计划产量为0时，达成率应为0%
      if (item.planQty === 0) return 0;
      return parseFloat(((item.productYeild / item.planQty) * 100).toFixed(2));
    });

    // 更新图表数据
    chart.setOption({
      xAxis: {
        data: xAxisData
      },
      series: [
        {
          name: "目标产量",
          data: planData
        },
        {
          name: "实际产量",
          data: actualData
        },

        {
          name: "达成率",
          data: rateData
        }
      ],
      tooltip: {
        formatter: function (params) {
          let result = params[0].name + '<br/>';
          params.forEach(param => {
            // 获取正确的颜色
            let color;
            if (param.seriesName === '实际产量') {
              color = "#23B5B1"; // 实际产量的颜色
            } else if (param.seriesName === '目标产量') {
              color = "#6495F9"; // 目标产量的颜色
            } else if (param.seriesName === '达成率') {
              color = "#F7B500"; // 达成率的颜色
            } else {
              color = param.color; // 其他情况使用默认颜色
            }

            // 创建颜色标记
            const colorSpan = `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${color};"></span>`;

            // 为达成率添加百分号
            let value = param.value;
            if (param.seriesName === '达成率') {
              value = value + '%';
            }

            result += colorSpan + param.seriesName + ': ' + value + '<br/>';
          });
          return result;
        }
      }
    });
  } catch (error) {
    console.error('更新日生产计划执行率图表数据异常:', error);
  }
};

/**
 * 更新直通率图表数据
 * @param {echarts.ECharts} chart - 图表实例
 * @param {Object} data - 接口返回的数据
 * @param {boolean} darkTheme - 是否为暗色主题
 * @returns {void}
 */
export const updateFtyChartData = (chart, data, darkTheme) => {
  if (!chart) return;

  // 解构数据
  const { totalProductQty, totalDefectQty, totalPassThroughRate, tables } = data;

  // 文本颜色根据主题设置
  const textColor = darkTheme ? "#ffffff" : "#333333";

  // 准备详情数据 - 使用tables数据
  const xData = tables && tables.length > 0
    ? tables.map(item => item.causeName)
    : ['暂无详细数据'];

  const detailData = tables && tables.length > 0
    ? tables.map(item => item.totalDefectQty)
    : [0];

  // 准备折线图数据 - 使用tables数据中的spiltTotalProductQty
  const lineData = tables && tables.length > 0
    ? tables.map(item => item.spiltTotalProductQty)
    : [0];

  // 设置图表选项
  const option = {
    title: {
      text: '直通率',
      subtext: `良品率: ${totalPassThroughRate || '0'}% (不良数: ${totalDefectQty || 0} / 产量: ${totalProductQty || 0})`,
      left: 'center',
      textStyle: {
        color: textColor
      },
      subtextStyle: {
        color: textColor
      },
      show: false
    },
    grid: {
      left: "2%",
      right: "4%",
      bottom: "3%",
      top: "15%",
      containLabel: true,
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
        label: {
          backgroundColor: '#fff',
          color: '#333',
          borderColor: darkTheme ? "rgba(0, 170, 166, 0.5)" : "#ddd"
        }
      },
      backgroundColor: darkTheme ? "#08343C" : "#fff",
      borderColor: darkTheme ? "rgba(0, 170, 166, 0.5)" : "#ddd",
      borderWidth: 1,
      textStyle: {
        color: darkTheme ? "#FFFFFF" : "#333",
      }
    },
    legend: {
      data: ['不良数量', '分段不良数'],
      // left: 10,
      // top: 0,
      textStyle: {
        color: textColor
      }
    },
    toolbox: {
      feature: {
        dataZoom: {
          yAxisIndex: 'none'
        },
        restore: {},
        saveAsImage: {},
      }
    },
    xAxis: {
      type: "category",
      data: xData,
      axisLabel: {
        rotate: 45,
        fontSize: 10,
        color: textColor,
      },
      axisLine: {
        lineStyle: {
          color: textColor,
        },
      },
    },
    yAxis: {
      type: "value",
      splitLine: {
        lineStyle: {
          type: "dashed",
        },
      },
      axisLabel: {
        color: textColor,
      },
      axisLine: {
        lineStyle: {
          color: textColor,
        },
      },
    },
    series: [
      {
        name: "不良数量",
        type: "bar",
        data: detailData,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "#23B5B1" },
            { offset: 1, color: "rgba(35, 181, 177, 0.1)" },
          ]),
        },
        barWidth: 30,
        emphasis: {
          itemStyle: {
            borderColor: "#ffffff",
            borderWidth: 1,
          },
        },
        label: {
          show: true,
          position: 'top',
          color: textColor,
          formatter: '{c}',
          fontSize: 12
        }
      },
      {
        name: "分段不良数",
        type: "line",
        data: lineData,
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: "#6495F9"
        },
        lineStyle: {
          width: 2,
          type: 'solid'
        }
      }
    ]
  };

  // 更新图表
  chart.setOption(option, true);
};

/**
 * 更新焊接泄露率图表数据
 * @param {echarts.ECharts} chart - 图表实例
 * @param {Object} data - 接口返回的数据
 * @param {boolean} darkTheme - 是否为暗色主题
 * @returns {void}
 */
export const updateWeldingLeakChartData = (chart, data, darkTheme) => {
  if (!chart) return;

  // 解构数据
  const { currentLeakCount, currentProduction, currentLeakRate, tables } = data;

  // 文本颜色根据主题设置
  const textColor = darkTheme ? "#ffffff" : "#333333";

  // 准备详情数据 - 使用tables数据
  const xData = tables && tables.length > 0
    ? tables.map(item => item.causeName)
    : ['暂无详细数据'];

  const detailData = tables && tables.length > 0
    ? tables.map(item => item.weldingLeakQty)
    : [0];

  // 准备折线图数据 - 使用tables数据中的spiltWeldingLeakQty
  const lineData = tables && tables.length > 0
    ? tables.map(item => item.spiltWeldingLeakQty)
    : [0];

  // 设置图表选项
  const option = {
    title: {
      text: '焊接泄露率',
      subtext: `泄露率: ${currentLeakRate || '0'}% (泄露数: ${currentLeakCount || 0} / 产量: ${currentProduction || 0})`,
      left: 'center',
      textStyle: {
        color: textColor
      },
      subtextStyle: {
        color: textColor
      },
      show: false
    },
    grid: {
      left: "2%",
      right: "4%",
      bottom: "3%",
      top: "15%",
      containLabel: true,
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
        label: {
          backgroundColor: '#fff',
          color: '#333',
          borderColor: darkTheme ? "rgba(0, 170, 166, 0.5)" : "#ddd"
        }
      },
      backgroundColor: darkTheme ? "#08343C" : "#fff",
      borderColor: darkTheme ? "rgba(0, 170, 166, 0.5)" : "#ddd",
      borderWidth: 1,
      textStyle: {
        color: darkTheme ? "#FFFFFF" : "#333",
      }
    },
    legend: {
      data: ['泄露数量', '分段泄露数'],
      // right: 10,
      // top: 0,
      textStyle: {
        color: textColor
      }
    },
    toolbox: {
      feature: {
        dataZoom: {
          yAxisIndex: 'none'
        },
        restore: {},
        saveAsImage: {},
      }
    },
    xAxis: {
      type: "category",
      data: xData,
      axisLabel: {
        rotate: 45,
        fontSize: 10,
        color: textColor,
      },
      axisLine: {
        lineStyle: {
          color: textColor,
        },
      },
    },
    yAxis: {
      type: "value",
      splitLine: {
        lineStyle: {
          type: "dashed",
        },
      },
      axisLabel: {
        color: textColor,
      },
      axisLine: {
        lineStyle: {
          color: textColor,
        },
      },
    },
    series: [
      {
        name: "泄露数量",
        type: "bar",
        data: detailData,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "#23B5B1" },
            { offset: 1, color: "rgba(35, 181, 177, 0.1)" },
          ]),
        },
        barWidth: 30,
        emphasis: {
          itemStyle: {
            borderColor: "#ffffff",
            borderWidth: 1,
          },
        },
        label: {
          show: true,
          position: 'top',
          color: textColor,
          formatter: '{c}',
          fontSize: 12
        }
      },
      {
        name: "分段泄露数",
        type: "line",
        data: lineData,
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: "#6495F9"
        },
        lineStyle: {
          width: 2,
          type: 'solid'
        }
      }
    ]
  };

  // 更新图表
  chart.setOption(option, true);
};

/**
 * 更新OQC图表数据
 * @param {echarts.ECharts} chart - 图表实例
 * @param {Object} data - 接口返回的数据
 * @param {boolean} darkTheme - 是否为暗色主题
 * @returns {void}
 */
export const updateOqcChartData = (chart, data, darkTheme) => {
  if (!chart) return;

  // 解构数据
  const { rejectQty, outputQty, oqcRatio, detail } = data;

  // 文本颜色根据主题设置
  const textColor = darkTheme ? "#ffffff" : "#333333";

  // 准备详情数据
  const xData = detail && detail.length > 0
    ? detail.map(item => item.dpNm)
    : ['暂无详细数据'];

  const detailData = detail && detail.length > 0
    ? detail.map(item => item.quantity)
    : [0];

  // 设置图表选项
  const option = {
    title: {
      text: 'OQC抽检不良率',
      subtext: `不良率: ${oqcRatio || '0%'} (不良数: ${rejectQty || 0} / 抽检总数: ${outputQty || 0})`,
      left: 'center',
      textStyle: {
        color: textColor
      },
      subtextStyle: {
        color: textColor
      },
      show: false
    },
    grid: {
      left: "2%",
      right: "4%",
      bottom: "3%",
      top: "15%",
      containLabel: true,
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
        label: {
          backgroundColor: '#fff',
          color: '#333',
          borderColor: darkTheme ? "rgba(0, 170, 166, 0.5)" : "#ddd"
        }
      },
      backgroundColor: darkTheme ? "#08343C" : "#fff",
      borderColor: darkTheme ? "rgba(0, 170, 166, 0.5)" : "#ddd",
      borderWidth: 1,
      textStyle: {
        color: darkTheme ? "#FFFFFF" : "#333",
      },
      formatter: function (params) {
        const item = params[0];
        return `${item.name}: ${item.value} (${(item.value / (outputQty || 1) * 100).toFixed(2)}%)`;
      }
    },
    xAxis: {
      type: "category",
      data: xData,
      axisLabel: {
        rotate: 45,
        fontSize: 10,
        color: textColor,
      },
      axisLine: {
        lineStyle: {
          color: textColor,
        },
      },
    },
    yAxis: {
      type: "value",
      splitLine: {
        lineStyle: {
          type: "dashed",
        },
      },
      axisLabel: {
        color: textColor,
      },
      axisLine: {
        lineStyle: {
          color: textColor,
        },
      },
    },
    series: [
      {
        name: "不良数量",
        type: "bar",
        data: detailData,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "#23B5B1" },
            { offset: 1, color: "rgba(35, 181, 177, 0.1)" },
          ]),
        },
        barWidth: 30,
        emphasis: {
          itemStyle: {
            borderColor: "#ffffff",
            borderWidth: 1,
          },
        },
        label: {
          show: true,
          position: 'top',
          color: textColor,
          formatter: '{c}',
          fontSize: 12
        }
      }
    ],
  };

  // 更新图表
  chart.setOption(option, true);
};

/**
 * 更新停产损失时间图表数据
 * @param {echarts.ECharts} chart - 图表实例
 * @param {Object} data - 接口返回的数据
 * @param {boolean} darkTheme - 是否为暗色主题
 * @returns {void}
 */
export const updateDowntimeLossChartData = (chart, data, darkTheme) => {
  if (!chart) return;

  // 文本颜色根据主题设置
  const textColor = darkTheme ? "#ffffff" : "#333333";

  // 准备图表数据 - 使用tables数据
  const xData = data.tables && data.tables.length > 0
    ? data.tables.map(item => item.pointDesc)
    : ['暂无详细数据'];

  const detailData = data.tables && data.tables.length > 0
    ? data.tables.map(item => item.totalTime)
    : [0];

  // 准备折线图数据 - 使用tables数据中的spiltTotalTime
  const lineData = data.tables && data.tables.length > 0
    ? data.tables.map(item => item.spiltTotalTime)
    : [0];

  // 设置图表选项
  const option = {
    title: {
      text: '停产损失时间',
      left: 'center',
      textStyle: {
        color: textColor
      },
      show: false
    },
    grid: {
      left: "2%",
      right: "4%",
      bottom: "3%",
      top: "15%",
      containLabel: true,
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
        label: {
          backgroundColor: '#fff',
          color: '#333',
          borderColor: darkTheme ? "rgba(0, 170, 166, 0.5)" : "#ddd"
        }
      },
      backgroundColor: darkTheme ? "#08343C" : "#fff",
      borderColor: darkTheme ? "rgba(0, 170, 166, 0.5)" : "#ddd",
      borderWidth: 1,
      textStyle: {
        color: darkTheme ? "#FFFFFF" : "#333",
      }
    },
    legend: {
      data: ['停产时间(分钟)', '分段停产时间'],
      // right: 10,
      // top: 0,
      textStyle: {
        color: textColor
      }
    },
    toolbox: {
      feature: {
        dataZoom: {
          yAxisIndex: 'none'
        },
        restore: {},
        saveAsImage: {},
      }
    },
    xAxis: {
      type: "category",
      data: xData,
      axisLabel: {
        rotate: 45,
        fontSize: 10,
        color: textColor,
      },
      axisLine: {
        lineStyle: {
          color: textColor,
        },
      },
    },
    yAxis: {
      type: "value",
      splitLine: {
        lineStyle: {
          type: "dashed",
        },
      },
      axisLabel: {
        color: textColor,
      },
      axisLine: {
        lineStyle: {
          color: textColor,
        },
      },
    },
    series: [
      {
        name: "停产时间(分钟)",
        type: "bar",
        data: detailData,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "#23B5B1" },
            { offset: 1, color: "rgba(35, 181, 177, 0.1)" },
          ]),
        },
        barWidth: 30,
        emphasis: {
          itemStyle: {
            borderColor: "#ffffff",
            borderWidth: 1,
          },
        },
        label: {
          show: true,
          position: 'top',
          color: textColor,
          formatter: '{c}',
          fontSize: 12
        }
      },
      {
        name: "分段停产时间",
        type: "line",
        data: lineData,
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: "#6495F9"
        },
        lineStyle: {
          width: 2,
          type: 'solid'
        }
      }
    ],
  };

  // 更新图表
  chart.setOption(option, true);
};

/**
 * 更新过站损失时间图表数据
 * @param {echarts.ECharts} chart - 图表实例
 * @param {Object} data - 接口返回的数据
 * @param {boolean} darkTheme - 是否为暗色主题
 * @returns {void}
 */
export const updateStationLossChartData = (chart, data, darkTheme) => {
  if (!chart) return;

  // 文本颜色根据主题设置
  const textColor = darkTheme ? "#ffffff" : "#333333";

  // 准备图表数据 - 使用tables数据
  const xData = data.tables && data.tables.length > 0
    ? data.tables.map(item => item.stationName)
    : ['暂无详细数据'];

  const detailData = data.tables && data.tables.length > 0
    ? data.tables.map(item => item.totalLostTime)
    : [0];

  // 由于接口没有提供分段数据，我们使用累计损失次数作为折线图数据
  const lineData = data.tables && data.tables.length > 0
    ? data.tables.map(item => item.totalLostNum)
    : [0];

  // 设置图表选项
  const option = {
    title: {
      text: '过站损失时间',
      subtext: `总损失时间: ${data.totalLostTime || 0}分钟 (总停线次数: ${data.totalLostNum || 0})`,
      left: 'center',
      textStyle: {
        color: textColor
      },
      subtextStyle: {
        color: textColor
      },
      show: false
    },
    grid: {
      left: "2%",
      right: "4%",
      bottom: "3%",
      top: "15%",
      containLabel: true,
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
        label: {
          backgroundColor: '#fff',
          color: '#333',
          borderColor: darkTheme ? "rgba(0, 170, 166, 0.5)" : "#ddd"
        }
      },
      backgroundColor: darkTheme ? "#08343C" : "#fff",
      borderColor: darkTheme ? "rgba(0, 170, 166, 0.5)" : "#ddd",
      borderWidth: 1,
      textStyle: {
        color: darkTheme ? "#FFFFFF" : "#333",
      }
    },
    legend: {
      data: ['损失时间(分钟)', '损失次数'],
      // right: 10,
      // top: 0,
      textStyle: {
        color: textColor
      }
    },
    toolbox: {
      feature: {
        dataZoom: {
          yAxisIndex: 'none'
        },
        restore: {},
        saveAsImage: {},
      }
    },
    xAxis: {
      type: "category",
      data: xData,
      axisLabel: {
        rotate: 45,
        fontSize: 10,
        color: textColor,
      },
      axisLine: {
        lineStyle: {
          color: textColor,
        },
      },
    },
    yAxis: {
      type: "value",
      splitLine: {
        lineStyle: {
          type: "dashed",
        },
      },
      axisLabel: {
        color: textColor,
      },
      axisLine: {
        lineStyle: {
          color: textColor,
        },
      },
    },
    series: [
      {
        name: "损失时间(分钟)",
        type: "bar",
        data: detailData,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "#6495F9" },
            { offset: 1, color: "rgba(100, 149, 249, 0.1)" },
          ]),
        },
        barWidth: 30,
        emphasis: {
          itemStyle: {
            borderColor: "#ffffff",
            borderWidth: 1,
          },
        },
        label: {
          show: true,
          position: 'top',
          color: textColor,
          formatter: '{c}',
          fontSize: 12
        }
      },
      {
        name: "损失次数",
        type: "line",
        data: lineData,
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        itemStyle: {
          color: "#23B5B1"
        },
        lineStyle: {
          width: 2,
          type: 'solid'
        }
      }
    ],
  };

  // 更新图表
  chart.setOption(option, true);
};
